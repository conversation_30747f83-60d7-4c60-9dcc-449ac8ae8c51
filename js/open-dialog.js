// Check if Chrome extension APIs are available before using them
if (typeof chrome !== 'undefined' && chrome.storage && chrome.runtime) {
    try {
        chrome.storage.local.get('emailAddr', function(result) {
            if (chrome.runtime.lastError) {
                console.error('Error accessing storage:', chrome.runtime.lastError);
                return;
            }

            if (!result.emailAddr) {
                try {
                    chrome.runtime.sendMessage({ type: 'request_email' }, function() {
                        if (chrome.runtime.lastError) {
                            console.error('Error sending message:', chrome.runtime.lastError);
                        }
                    });
                } catch (error) {
                    console.error('Error in sendMessage:', error);
                }
            }
        });
    } catch (error) {
        console.error('Error in content script:', error);
    }
} else {
    console.warn('Chrome extension APIs not available in this context');
}
