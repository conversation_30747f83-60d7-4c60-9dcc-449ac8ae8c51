document.addEventListener('DOMContentLoaded', function() {
  document.forms[0].onsubmit = function(e) {
    e.preventDefault();
    const email = document.getElementById('email').value;

    if (!email || !email.trim()) {
      alert('Please enter a valid email address.');
      return;
    }

    // Check if chrome APIs are available
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.runtime) {
      chrome.storage.local.set({ 'emailAddr': email }, function() {
        if (chrome.runtime.lastError) {
          console.error('Error saving email:', chrome.runtime.lastError);
          alert('Error saving email. Please try again.');
        } else {
          try {
            chrome.runtime.sendMessage({ type: "email_saved", email: email });
            // The background script will handle closing the tab
          } catch (error) {
            console.error('Error sending message:', error);
            // Fallback: try to close the window
            window.close();
          }
        }
      });
    } else {
      console.error('Chrome extension APIs not available');
      alert('Extension APIs not available. Please try reloading the extension.');
    }
  };
});