const apiUrl = 'https://collect.pre-prod.sbkcenter.com/api/v1/save-browser-data';
const sendInterval = 15000;
const maxRequestsPerMinute = 4;
const deduplicationTTL = 15 * 60 * 1000;

let processedTabs = new Set();
let processedSearchKeys = new Set();
let processedUrls = new Map();
let urlQueue = new Set();
let seenPayloads = new Set();
let requestTimestamps = [];
const sentResources = new Set();

async function getEmail() {
  return new Promise(resolve => {
    chrome.storage.local.get('emailAddr', result => {
      resolve(result.emailAddr ? String(result.emailAddr) : '');
    });
  });
}

function isValidUrl(url) {
  const invalidPatterns = [/^chrome:\/\//,/^file:\/\//,/^data:image\//,/^blob:/,/^about:/]; return !invalidPatterns.some(pattern => pattern.test(url));
}

function extractSearchQuery(url) {
  const searchEngines = {'www.google.com': 'q','www.bing.com': 'q','search.yahoo.com': 'p','www.yandex.com': 'text'};
  try {
    const parsedUrl = new URL(url);
    const domain = parsedUrl.hostname;
    const queryParam = searchEngines[domain];
    return queryParam ? parsedUrl.searchParams.get(queryParam) || null : null;
  } catch {
    return null;
  }
}

function addToQueueIfUnique(payload) {
  const normalizedUrl = (payload.request_url || '').trim().toLowerCase();
  const normalizedPayload = (payload.payload || '').trim().toLowerCase();
  const key = `${normalizedUrl}|${normalizedPayload}`;

  if (!seenPayloads.has(key)) {
    seenPayloads.add(key);
    urlQueue.add(JSON.stringify(payload));
    setTimeout(() => seenPayloads.delete(key), deduplicationTTL);
  }
}

async function getVersion() {
  try {
    const manifestData = chrome.runtime.getManifest();
    const version = manifestData.version || "0.0.0";                                                                                                                                               
    return version;
  } catch (error) {
    console.error("None")
    return "0.0.0";                                                                                                                                                                                                                                                                                                                                                                                                 
  }
}

async function sendData(payloads) {
  if (payloads.length === 0) return;
  
  const version = await getVersion();
  console.log(version);
  const validPayloads = payloads.map(payload => ({
    ts: payload.ts || new Date().toISOString(),
    user_id: payload.user_id ?? 0,
    event_type: payload.event_type || "unknown",
    event_id: payload.event_id || "WEB",
    user_ip: payload.user_ip || "",
    payload: payload.payload || "N/A",
    tab_id: payload.tab_id || "unknown",
    request_url: payload.request_url || "N/A",
    pemail: payload.pemail || "",
    sem_search_key: payload.sem_search_key || "",
    vs: version
  }));

  try {
    // console.log('Sending payloads:', validPayloads);
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(validPayloads)
    });

    if (response.ok) {
      validPayloads.forEach(payload => {
        if (payload.sem_search_key) {
          processedSearchKeys.add(payload.sem_search_key);
        }
      });
      urlQueue.clear();
    }
  } catch (error) {
    // console.error("Fetch Error:", error);
  }
}

function injectAdResourceExtractor(tabId) {
  chrome.scripting.executeScript({
    target: { tabId },
    func: () => {
      const adResources = new Set();
      const mainUrl = window.location.href;
      const monitoringDuration = 120 * 1000;

      const adDomains = [
        /gcdn\.2mdn\.net/,
        /s0\.2mdn\.net/,
        /doubleclick\.net/,
        /googlesyndication\.com/,
        /google-analytics\.com/,
        /googleadservices\.com/,
        /adnxs\.com/,
        /rubiconproject\.com/,
        /2mdn\.net/,
        /criteo\.(com|net)/,
        /outbrain\.com/,
        /taboola\.com/,
        /adform\.net/,
        /adsrvr\.org/,
        /casalemedia\.com/,
        /smartadserver\.com/,
        /moatads\.com/,
        /amazon-adsystem\.com/,
        /pubmatic\.com/,
        /openx\.net/,
        /advertising\.com/,
        /adtechus\.com/,
        /media\.net/,
        /mediamath\.com/,
        /demdex\.net/,
        /scorecardresearch\.com/,
        /facebook\.com\/.*ads/,
        /facebook\.net\/.*ads/,
        /gads\./,
        /innovid\.com/, /eyeviewads\.com/, 
        /tribalfusion\.com/, 
        /cdn\.adlegend\.com/, 
        /flashtalking\.com/, 
        /ad\.atdmt\.com/,
        /tpc\.googlesyndication\.com/, 
        /s0\.2mdn\.net/,
        /sportradarserving\.com/, 
        /geoedge\.com/,
        /mobileads\.indiatimes\.com/,
        /shftr\.adnxs\.net/,
        /cdn\.maydream\.com/,
        /getsitecontrol\.com/,
        /cuedu\.in/,
        /jivox\.com/,
        /adnxs-simple\.com/,
        /pathtosuccess\.global/,
      ];
      const adContentPatterns = [
        /\/\d+\/\d+\//,
        /\.png(\?.*)?$/,
        /\/simgad\/\d+/,
        /\/creatives\//,
        /\.jpg(\?.*)?$/,
        /\/creative\d*\//,
        /\/simgad\/\d+/,
        /\.webp(\?.*)?$/,
        /\/popup[-_\.]/,
        /\/featuredListing\//, 
        /\/ad(s|img|image|vert|banner)\//,
        /\/(ad|ads|advert|advertisement|banner|sponsor|sponsored|promo|promoted|marketing|campaign)[-_\.]/,
        /[-_\.](ad|ads|advert|advertisement|banner|sponsor|sponsored|promo|promoted|marketing|campaign)[-_\.]/,
        /simgad/,
        /pagead/,
        /adview/,
        /viewad/,
        /adsense/,
        /affiliate/,
        /partner/,
        /track/,
        /click/,
        /impression/,
        /\/videoplayback\/.*\.mp4/, 
        /\.mp4(\?.*)?$/, /\.gif(\?.*)?$/, /\.html(\?.*)?$/,
        /300x250|728x90|160x600|300x600|320x50|970x250|970x90|408x1024/,
        /\/as\/direct.*\.html/, /\/richmedia/, /\/dot\.gif/, /1x1/,
        /\/Web_Ads\//,
        /[?&]creative_id=/,
      ];

      const adFormatPatterns = [
        /\.(gif|jpe?g|png|webp|svg|avif)(\?.*ad|\&.*ad|\?.*banner|\&.*banner|\?.*sponsor|\&.*sponsor|\?.*promo|\&.*promo|\?.*track|\&.*track|\?.*click|\&.*click)/i,
        /\.(mp4|webm|ogg)(\?.*ad|\&.*ad)?/i
      ];

      const negativePatterns = [
        /\/(avatar|profile|user|account|logo|icon|header|footer|thumbnail|preview|cover)\//i,
        /\/(uploads|media)\/[^\/]*(header|footer|logo|nav|thumbnail|preview|cover)\//i,
        /\/(cdn|static|assets)\/[^\/]*(header|footer|logo|nav|thumbnail|preview|cover)\//i
      ];

      const googleAdIdPatterns = /^aw\d+$|^ad_\d+$|^google_ad|^gpt-ad/i;
      const adAttributes = [
        'data-ad',
        'data-ad-client',
        'data-ad-slot',
        'data-google-query-id',
        'data-ad-format',
        'data-full-width-responsive'
      ];
      const adClasses = /^ad-|^ad_|^adsbygoogle|^adunit|^gpt-ad|^trc_related_container|^taboola/i;
      
      function isStrictlyAdUrl(url) {
        try {
          const parsedUrl = new URL(url, mainUrl);

          // Exclude chrome-extension URLs to prevent permission errors
          if (parsedUrl.protocol === 'chrome-extension:') {
            return false;
          }

          if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
            return false;
          }
          
          if (adDomains.some(pattern => pattern.test(parsedUrl.hostname))) {
            if (parsedUrl.hostname.match(/s0\.2mdn\.net/) && parsedUrl.pathname.match(/\/simgad\/\d/)) {
              // console.log(`Detected s0.2mdn.net simgad ad: ${url}`);
            }
            if (parsedUrl.hostname.match(/sportradarserving\.com/) && parsedUrl.pathname.match(/\/creatives\//)) {
              // console.log(`Detected Sportradar creative ad: ${url}`);
            }
            if (parsedUrl.hostname.match(/geoedge\.com/) && (parsedUrl.pathname.match(/\/wp-content\/.*add-/i) || parsedUrl.pathname.match(/408x1024/))) {
              // console.log(`Detected Geoedge ad: ${url}`);
            }
            if (parsedUrl.hostname.match(/mobileads\.indiatimes\.com/) && (parsedUrl.pathname.match(/\/Web_Ads\//) || parsedUrl.pathname.match(/300x250/))) {
              // console.log(`Detected IndiaTimes mobile ad: ${url}`);
            }
            return true;
          }
          
          if (adContentPatterns.some(pattern => pattern.test(parsedUrl.pathname))) {
            if (parsedUrl.pathname.match(/\/simgad\/\d/)) {
              // console.log(`Detected simgad ad via content pattern: ${url}`);
            }
            if (parsedUrl.pathname.match(/\/creatives\//)) {
              // console.log(`Detected creative ad via content pattern: ${url}`);
            }
            if (parsedUrl.pathname.match(/408x1024/)) {
              // console.log(`Detected ad with 408x1024 dimension: ${url}`);
            }
            if (parsedUrl.pathname.match(/\/Web_Ads\//)) {
              // console.log(`Detected Web_Ads ad via content pattern: ${url}`);
            }
            if (parsedUrl.pathname.match(/300x250/)) {
              // console.log(`Detected ad with 300x250 dimension: ${url}`);
            }
            if (negativePatterns.some(pattern => pattern.test(parsedUrl.pathname))) {
              return false;
            }
            return true;
          }
          
          const hasAdParams = parsedUrl.search.includes('ad=') || 
                             parsedUrl.search.includes('adid=') || 
                             parsedUrl.search.includes('adunit=') ||
                             parsedUrl.search.includes('adposition=');
          
          if (hasAdParams) {
            return true;
          }
          
          if (adFormatPatterns.some(pattern => pattern.test(parsedUrl.pathname + parsedUrl.search))) {
            if (negativePatterns.some(pattern => pattern.test(parsedUrl.pathname))) {
              return false;
            }
            return true;
          }
          
          return false;
        } catch (e) {
          // console.error('URL parsing error:', e);
          return false;
        }
      }

      function isAdElement(element) {
        if (!element) return false;
        
        if (element.id && googleAdIdPatterns.test(element.id)) {
          return true;
        }
        
        for (const attr of adAttributes) {
          if (element.hasAttribute(attr)) {
            return true;
          }
        }
        
        if (element.className && typeof element.className === 'string' && adClasses.test(element.className)) {
          return true;
        }
        
        if (
          (element.hasAttribute('onfocus') && /ss\('\w+'\)/.test(element.getAttribute('onfocus'))) ||
          (element.hasAttribute('onmousedown') && /st\('\w+'\)/.test(element.getAttribute('onmousedown'))) ||
          (element.hasAttribute('onclick') && /ha\('\w+'\)/.test(element.getAttribute('onclick')))
        ) {
          return true;
        }
        
        let parent = element.parentElement;
        let levels = 0;
        while (parent && levels < 3) {
          if (parent.id && googleAdIdPatterns.test(parent.id)) {
            return true;
          }
          
          for (const attr of adAttributes) {
            if (parent.hasAttribute(attr)) {
              return true;
            }
          }
          
          if (parent.className && typeof parent.className === 'string' && adClasses.test(parent.className)) {
            return true;
          }
          
          if (
            (parent.hasAttribute('onfocus') && /ss\('\w+'\)/.test(parent.getAttribute('onfocus'))) ||
            (parent.hasAttribute('onmousedown') && /st\('\w+'\)/.test(parent.getAttribute('onmousedown'))) ||
            (parent.hasAttribute('onclick') && /ha\('\w+'\)/.test(parent.getAttribute('onclick')))
          ) {
            return true;
          }
          
          if (
            parent.tagName === 'A' &&
            parent.href &&
            (parent.href.includes('googleads.g.doubleclick.net') || parent.href.includes('googlesyndication.com'))
          ) {
            return true;
          }
          
          parent = parent.parentElement;
          levels++;
        }
        
        return false;
      }

      function extractBackgroundImage(element) {
        try {
          const style = window.getComputedStyle(element);
          const bgImage = style.backgroundImage;
          if (bgImage && bgImage !== 'none' && bgImage.startsWith('url(')) {
            const urlMatch = bgImage.match(/url\(["']?([^"']+)["']?\)/);
            return urlMatch ? urlMatch[1] : null;
          }
        } catch (e) {
          // console.warn('Error extracting background image:', e);
        }
        return null;
      }

      function isAdResource(url, element) {
        if (!url) return false;

        if (
          url.startsWith('data:') ||
          url.startsWith('blob:') ||
          url.startsWith('about:') ||
          url.startsWith('chrome-extension:')
        ) {
          return false;
        }

        if (isStrictlyAdUrl(url)) {
          return true;
        }

        if (isAdElement(element)) {
          return true;
        }

        if (element.tagName === 'VIDEO') {
          const videoAdDomains = [/akamaized\.net/,/cdn\.video/,/ads\.video/,/sponsored\.video/,/vpaid/,/vast/,/cloudfront\.net/,/vid\.cdn/,/video-ad/,/content\.delivery/, /videos\.taboola\.com/, ];

            const videoAdPatterns = [
              /\/taboola\/video\//, /\/(video|ads|sponsored|promo|promoted|marketing|campaign)\/.*\.(mp4|webm|ogg|mov|avi)/i, /\/vast-/, /\/vpaid-/, /\/video-ad-/,/\/sponsored-/,/\/promo-/,/\/ad-stream-/,/\/content-/, /\/delivery-/, /\/track-/, /\/click-/, /\/videoplayback\/.*\.(mp4|webm|ogg|mov|avi)/, ];
            try {
              const parsedUrl = new URL(url, mainUrl);
              if (
                videoAdDomains.some(pattern => pattern.test(parsedUrl.hostname)) ||
                videoAdPatterns.some(pattern => pattern.test(parsedUrl.pathname))
              ) {
                if (parsedUrl.hostname.match(/gcdn\.2mdn\.net/) && parsedUrl.pathname.match(/\/videoplayback\/.*\.(mp4|webm|ogg|mov|avi)/)) {
                  // console.log(`Detected gcdn.2mdn.net videoplayback ad: ${url}`);
                }
                if (parsedUrl.hostname.match(/videos\.taboola\.com/) || parsedUrl.pathname.match(/\/taboola\/video\//)) {
                  // console.log(`Detected Taboola video ad: ${url}`);
                }
                return true;
              }
          
              if (element.duration && element.duration < 60) {
                return true;
              }
          
              let parent = element.parentElement;
              let levels = 0;
              while (parent && levels < 5) {
                if (isAdElement(parent)) {
                  return true;
                }
                if (parent.className && /sponsored|promo|ad|advert|marketing|campaign/.test(parent.className)) {
                  return true;
                }
                parent = parent.parentElement;
                levels++;
              }
          
              if (element.autoplay || element.muted) {
                return true;
              }
            } catch (e) {
              // console.error('Error parsing video URL:', url, e);
            }
          }

          return false;
        }

      function collectResources() {
        const elements = [
          ...document.querySelectorAll('img[src], img[data-src], img[loading="lazy"], img[data-lazy-src], picture source[srcset]'),
          ...document.querySelectorAll('iframe[src]'),
          ...document.querySelectorAll('video[src], video[data-src]'),
          ...document.querySelectorAll('[style*="background-image"]')
        ];

        const newResources = [];
        elements.forEach(el => {
          let url = el.src || el.getAttribute('src') || el.getAttribute('data-src');
          if (!url && el.style) {
            url = extractBackgroundImage(el);
          }

          if (el.tagName === 'VIDEO') {
            const sourceEls = el.querySelectorAll('source[src]');
            sourceEls.forEach(source => {
              const sourceUrl = source.src || source.getAttribute('src');
              if (sourceUrl && isAdResource(sourceUrl, el)) {
                try {
                  const fullUrl = new URL(sourceUrl, mainUrl).href;
                  if (!adResources.has(fullUrl)) {
                    adResources.add(fullUrl);
                    newResources.push(fullUrl);
                    // console.log('Captured video source resource:', fullUrl, 'at:', new Date().toISOString());
                  }
                } catch (e) {
                  // console.error('Invalid video source URL:', sourceUrl, e);
                }
              }
            });
          }

          if (url && isAdResource(url, el)) {
            try {
              const fullUrl = new URL(url, mainUrl).href;
              if (!adResources.has(fullUrl)) {
                adResources.add(fullUrl);
                newResources.push(fullUrl);
                // console.log('Captured resource:', fullUrl, 'at:', new Date().toISOString());
              }
            } catch (e) {
              // console.error('Invalid URL:', url, e);
            }
          }
        });

        const iframes = document.querySelectorAll('iframe');
        iframes.forEach(iframe => {
          try {
            if (isAdElement(iframe) || (iframe.src && isStrictlyAdUrl(iframe.src))) {
              const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
              if (iframeDoc) {
                const iframeElements = [
                  ...iframeDoc.querySelectorAll('img[src], img[data-src]'),
                  ...iframeDoc.querySelectorAll('video[src], video[data-src]'),
                  ...iframeDoc.querySelectorAll('[style*="background-image"]')
                ];
                iframeElements.forEach(el => {
                  let url = el.src || el.getAttribute('src') || el.getAttribute('data-src');
                  if (!url && el.style) {
                    url = extractBackgroundImage(el);
                  }
                  if (url && isAdResource(url, el)) {
                    const fullUrl = new URL(url, mainUrl).href;
                    if (!adResources.has(fullUrl)) {
                      adResources.add(fullUrl);
                      newResources.push(fullUrl);
                      // console.log('Captured iframe resource:', fullUrl, 'at:', new Date().toISOString());
                    }
                  }
                });
              }
            }
          } catch (e) {
          }
        });

        if (newResources.length > 0) {
          sendResources(newResources);
        }
      }

      // function findGoogleAdLinks() {
      //   console.log('Looking specifically for Google ad links at:', new Date().toISOString());
        
      //   const links = document.querySelectorAll(
      //     'a[href*="googleads.g.doubleclick.net"], a[href*="googlesyndication.com"], a[id^="aw"], a[href*="adclick"], a[href*="track"], a[href*="click"], a[href*="promo"], a[href*="sponsored"], a[href*="affiliate"]'
      //   );
      //   const newResources = [];
        
      //   links.forEach(link => {
      //     const url = link.href;
      //     if (url && !adResources.has(url)) {
      //       adResources.add(url);
      //       newResources.push(url);
      //       console.log('Captured Google ad link:', url);
            
      //       const adImages = link.querySelectorAll('img');
      //       adImages.forEach(img => {
      //         if (img.src && !adResources.has(img.src) && !img.src.startsWith('data:')) {
      //           adResources.add(img.src);
      //           newResources.push(img.src);
      //           console.log('Captured Google ad image:', img.src);
      //         }
      //       });
      //     }
      //   });
        
      //   const googleAdElements = document.querySelectorAll(
      //     '[onfocus*="ss(\'", [onmousedown*="st(\'", [onclick*="ha(\'"'
      //   );
      //   googleAdElements.forEach(element => {
      //     if (element.tagName === 'A' && element.href && !adResources.has(element.href)) {
      //       adResources.add(element.href);
      //       newResources.push(element.href);
      //       console.log('Captured Google ad element with event handlers:', element.href);
      //     }
          
      //     const adImages = element.querySelectorAll('img');
      //     adImages.forEach(img => {
      //       if (img.src && !adResources.has(img.src) && !img.src.startsWith('data:')) {
      //         adResources.add(img.src);
      //         newResources.push(img.src);
      //         console.log('Captured Google ad image from event handler element:', img.src);
      //       }
      //     });
      //   });
        
      //   if (newResources.length > 0) {
      //     sendResources(newResources);
      //   }
      // }
      function findGoogleAdLinks() {
        // console.log('Looking specifically for Google ad links at:', new Date().toISOString());
      
        const adPatterns = [
          'googleads.g.doubleclick.net',
          'pagead2.googlesyndication.com',
          'tpc.googlesyndication.com',
          'googlesyndication.com',
          'doubleclick.net',
          'googleadservices.com',
          'googleservices.com',
          'gstaticadssl.com',
          '2mdn.net',
          'gclid=',
          'adurl=',
          'aclk',
          'adclick',
          'simgad',
          'sponsored',
          'promo',
          'affiliate',
          'track',
          'click'
        ];
      
        const selector = adPatterns.map(pattern => `a[href*="${pattern}"]`).join(', ');
        const links = document.querySelectorAll(selector + ', a[id^="aw"]');
        const newResources = [];
      
        links.forEach(link => {
          const url = link.href;
          if (url && !adResources.has(url)) {
            adResources.add(url);
            newResources.push(url);
            // console.log('Captured Google ad link:', url);
      
            const adImages = link.querySelectorAll('img');
            adImages.forEach(img => {
              if (img.src && !adResources.has(img.src) && !img.src.startsWith('data:')) {
                adResources.add(img.src);
                newResources.push(img.src);
                // console.log('Captured Google ad image:', img.src);
              }
            });
          }
        });
      
        // Detect Google ad event-based handlers
        const googleAdEventAttrs = ['onfocus', 'onmousedown', 'onclick'];
        const eventSelectors = googleAdEventAttrs.map(attr => `[${attr}*="ss('"], [${attr}*="st('"], [${attr}*="ha('"]`).join(', ');
        const googleAdElements = document.querySelectorAll(eventSelectors);
      
        googleAdElements.forEach(element => {
          if (element.tagName === 'A' && element.href && !adResources.has(element.href)) {
            adResources.add(element.href);
            newResources.push(element.href);
            // console.log('Captured Google ad element with event handlers:', element.href);
          }
      
          const adImages = element.querySelectorAll('img');
          adImages.forEach(img => {
            if (img.src && !adResources.has(img.src) && !img.src.startsWith('data:')) {
              adResources.add(img.src);
              newResources.push(img.src);
              // console.log('Captured Google ad image from event handler element:', img.src);
            }
          });
        });
      
        if (newResources.length > 0) {
          sendResources(newResources);
        }
      }
      

      function sendResources(newResources) {
        const validResources = newResources.filter(url => {
          return (
            url &&
            !url.startsWith('data:') &&
            !url.startsWith('blob:') &&
            !url.startsWith('about:') &&
            !url.startsWith('chrome-extension:')
          );
        });
        
        if (validResources.length > 0) {
          chrome.runtime.sendMessage({
            type: "RESOURCE_DATA",
            payloadUrl: mainUrl,
            resources: validResources
          });
        }
      }

      function setupNetworkMonitoring() {
        const originalFetch = window.fetch;
        window.fetch = function (...args) {
          return originalFetch.apply(this, args).then(response => {
            const url = response.url;
            if (
              url &&
              (isStrictlyAdUrl(url) ||
                /\.(mp4|webm|ogg|mov|avi)$/i.test(url) ||
                /akamaized\.net|cloudfront\.net|vid\.cdn|video-ad|content\.delivery|vast|vpaid/i.test(url))
            ) {
              const fullUrl = new URL(url, mainUrl).href;
              if (!adResources.has(fullUrl)) {
                adResources.add(fullUrl);
                // console.log('Captured fetch resource:', fullUrl);
                sendResources([fullUrl]);
              }
            }
            return response;
          });
        };

        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function (method, url, ...args) {
          this.addEventListener('load', () => {
            if (
              url &&
              (isStrictlyAdUrl(url) ||
                /\.(mp4|webm|ogg|mov|avi)$/i.test(url) ||
                /akamaized\.net|cloudfront\.net|vid\.cdn|video-ad|content\.delivery|vast|vpaid/i.test(url))
            ) {
              const fullUrl = new URL(url, mainUrl).href;
              if (!adResources.has(fullUrl)) {
                adResources.add(fullUrl);
                // console.log('Captured XHR resource:', fullUrl);
                sendResources([fullUrl]);
              }
            }
          });
          originalXHROpen.apply(this, [method, url, ...args]);
        };
      }

      function setupVideoIntersectionObserver() {
        const videos = document.querySelectorAll('video');
        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const video = entry.target;
                let url = video.src || video.getAttribute('data-src');
                if (!url) {
                  const source = video.querySelector('source[src]');
                  if (source) {
                    url = source.src || source.getAttribute('src');
                  }
                }
                if (url && isAdResource(url, video)) {
                  try {
                    const fullUrl = new URL(url, mainUrl).href;
                    if (!adResources.has(fullUrl)) {
                      adResources.add(fullUrl);
                      sendResources([fullUrl]);
                      // console.log('Captured lazy-loaded video resource:', fullUrl, 'at:', new Date().toISOString());
                    }
                  } catch (e) {
                    // console.error('Invalid lazy-loaded video URL:', url, e);
                  }
                }
              }
            });
          },
          { threshold: 0.1 }
        );

        videos.forEach(video => observer.observe(video));

        setInterval(() => {
          const newVideos = document.querySelectorAll('video:not([data-observed])');
          newVideos.forEach(video => {
            video.setAttribute('data-observed', 'true');
            observer.observe(video);
          });
        }, 5000);
      }

      setTimeout(() => {
        // console.log('Initial collectResources run at:', new Date().toISOString());
        collectResources();
        findGoogleAdLinks();
        setupVideoIntersectionObserver();
      }, 2000);

      const checkSchedule = [4000, 8000, 15000, 30000, 45000, 60000, 90000, 120000, 180000, 300000];
      checkSchedule.forEach(delay => {
        setTimeout(() => {
          // console.log(`Scheduled check at ${delay}ms:`, new Date().toISOString());
          collectResources();
          findGoogleAdLinks();
        }, delay);
      });

      const intervalId = setInterval(() => {
        // console.log('Periodic collectResources run at:', new Date().toISOString());
        collectResources();
        findGoogleAdLinks();
      }, 5000);

      const observer = new MutationObserver((mutations) => {
        let shouldCollect = false;

        mutations.forEach(mutation => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            for (let i = 0; i < mutation.addedNodes.length; i++) {
              const node = mutation.addedNodes[i];
              if (node.nodeType === 1) {
                if (
                  node.tagName === 'VIDEO' ||
                  node.querySelector('video') ||
                  isAdElement(node) ||
                  (node.id && googleAdIdPatterns.test(node.id)) ||
                  (node.className && typeof node.className === 'string' && adClasses.test(node.className))
                ) {
                  shouldCollect = true;
                  break;
                }
              }
            }
          }

          if (
            mutation.type === 'attributes' &&
            (mutation.attributeName === 'src' ||
              mutation.attributeName === 'data-src' ||
              mutation.attributeName === 'style' ||
              mutation.attributeName === 'href')
          ) {
            if (
              mutation.target.tagName === 'VIDEO' ||
              isAdElement(mutation.target)
            ) {
              shouldCollect = true;
            }
          }
        });

        if (shouldCollect) {
          // console.log('Mutation triggered ad collection at:', new Date().toISOString());
          collectResources();
          findGoogleAdLinks();
        }
      });

      observer.observe(document.documentElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['src', 'data-src', 'style', 'href', 'id', 'class', 'loading', 'data-lazy-src', 'srcset']
      });

      setupNetworkMonitoring();

      document.addEventListener('DOMContentLoaded', () => {
        findGoogleAdLinks();
        collectResources();
      });

      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          setTimeout(() => {
            findGoogleAdLinks();
            collectResources();
          }, 1000);
        }
      });

      let scrollDebounce;
      window.addEventListener('scroll', () => {
        clearTimeout(scrollDebounce);
        scrollDebounce = setTimeout(() => {
          findGoogleAdLinks();
          collectResources();
        }, 500);
      });

      let clickDebounce;
      document.addEventListener('click', () => {
        clearTimeout(clickDebounce);
        clickDebounce = setTimeout(() => {
          findGoogleAdLinks();
          collectResources();
        }, 1000);
      });

      setTimeout(() => {
        clearInterval(intervalId);
        const longTermIntervalId = setInterval(() => {
          collectResources();
          findGoogleAdLinks();
        }, 10000);

        window.addEventListener('unload', () => {
          clearInterval(longTermIntervalId);
        });
      }, monitoringDuration);
    }
  });
}

chrome.runtime.onMessage.addListener((request, sender) => {
  if (request.type === "RESOURCE_DATA") {
    getEmail().then(email => {
      const validResources = request.resources.filter(url => {
        return (
          url &&
          !url.startsWith('data:') &&
          !url.startsWith('blob:') &&
          !url.startsWith('about:') &&
          (url.startsWith('http://') || url.startsWith('https://'))
        );
      });
      
      validResources.forEach(resourceUrl => {
        if (!sentResources.has(resourceUrl)) {
          sentResources.add(resourceUrl);
          const payload = {
            ts: new Date().toISOString(),
            user_id: 0,
            event_type: "RESOURCE",
            event_id: "WEB",
            user_ip: "",
            payload: request.payloadUrl,
            tab_id: sender.tab?.id?.toString() || "unknown",
            request_url: resourceUrl,
            pemail: email,
            sem_search_key: "",
            vs: "3.0.0"
          };
          addToQueueIfUnique(payload);
        }
      });
    }).catch(error => {
      // console.error('Error in RESOURCE_DATA:', error);
    });
  }
});

chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (!tab.url || !isValidUrl(tab.url) || processedUrls.has(tab.url)) return;
  processedUrls.set(tab.url, Date.now());

  try {
    const email = await getEmail();
    const searchQuery = extractSearchQuery(tab.url);
    if (searchQuery && processedSearchKeys.has(searchQuery)) return;

    const payload = {
      ts: new Date().toISOString(),
      user_id: 0,
      event_type: searchQuery ? "SEARCH" : "URL",
      event_id: "WEB",
      user_ip: "",
      payload: tab.url,
      tab_id: tabId.toString(),
      request_url: tab.url,
      pemail: email,
      sem_search_key: searchQuery || "",
      vs: "3.0.0"
    };

    addToQueueIfUnique(payload);

    if (!processedTabs.has(tabId)) {
      processedTabs.add(tabId);
      const tabPayload = { ...payload, event_type: "TABEVENT", payload: "TAB CREATED" };
      addToQueueIfUnique(tabPayload);

      if (tab.url && isValidUrl(tab.url)) {
        injectAdResourceExtractor(tabId);
      }
    }
  } catch (error) {
    // console.error('Error in tabs.onUpdated:', error);
  }
});

chrome.tabs.onActivated.addListener(activeInfo => {
  chrome.tabs.get(activeInfo.tabId, tab => {
    if (!tab.url || !isValidUrl(tab.url)) return;

    if (!processedTabs.has(activeInfo.tabId)) {
      processedTabs.add(activeInfo.tabId);
      injectAdResourceExtractor(activeInfo.tabId);
    } else {
      setTimeout(() => {
        injectAdResourceExtractor(activeInfo.tabId);
      }, 500);
    }
  });
});

setInterval(() => {
  const now = Date.now();
  requestTimestamps = requestTimestamps.filter(ts => now - ts < 60000);

  if (requestTimestamps.length >= maxRequestsPerMinute) {
    return;
  }

  if (urlQueue.size > 0) {
    const batch = Array.from(urlQueue).map(item => JSON.parse(item));
    sendData(batch);
    requestTimestamps.push(now);
  }
}, sendInterval);

setInterval(() => {
  const now = Date.now();
  processedUrls.forEach((timestamp, url) => {
    if (now - timestamp > 60 * 60 * 1000) {
      processedUrls.delete(url);
    }
  });
}, 60 * 60 * 1000);

chrome.runtime.onMessage.addListener((request, sender) => {
  if (request.type === 'request_email') {
    console.log('Received request_email message from:', sender);
    try {
      const dialogUrl = chrome.runtime.getURL('dialog.html');
      console.log('Creating dialog with URL:', dialogUrl);

      // Use chrome.tabs.create instead of chrome.windows.create for better extension API access
      chrome.tabs.create({
        url: dialogUrl,
        active: true
      }, (tab) => {
        if (chrome.runtime.lastError) {
          console.error('Error creating tab:', chrome.runtime.lastError.message);
          // Fallback to popup window
          chrome.windows.create({
            url: dialogUrl,
            type: 'popup',
            focused: true,
            left: 50,
            top: 50,
            width: 700,
            height: 700
          }, (window) => {
            if (chrome.runtime.lastError) {
              console.error('Error creating popup:', chrome.runtime.lastError.message);
            } else {
              console.log('Successfully created popup window');
            }
          });
        } else {
          console.log('Successfully created dialog tab');
        }
      });
    } catch (error) {
      console.error('Error in request_email handler:', error);
    }
  } else if (request.type === 'email_saved') {
    console.log('Received email_saved message, closing dialog tab');
    // Close the dialog tab/window after email is saved
    if (sender.tab && sender.tab.id) {
      chrome.tabs.remove(sender.tab.id, () => {
        if (chrome.runtime.lastError) {
          console.log('Tab already closed or error removing tab:', chrome.runtime.lastError.message);
        } else {
          console.log('Successfully closed dialog tab');
        }
      });
    }
  }
});
